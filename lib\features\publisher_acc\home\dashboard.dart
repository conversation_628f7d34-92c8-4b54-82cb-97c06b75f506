import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:guest_posts/core/theme/app_theme.dart'; // Using your AppTheme

// Note: The AppTheme class previously defined here has been removed.
// This widget now relies on the AppTheme provided from 'package:guest_posts/core/theme/app_theme.dart'
// and the ThemeData applied in your MaterialApp.

// Analytics enums and models
enum AnalyticsViewType { day, period }

class AnalyticsData {
  final DateTime date;
  final double amount;
  final int hour;

  AnalyticsData({
    required this.date,
    required this.amount,
    this.hour = 0,
  });
}

class HourlyAnalytics {
  final int hour;
  final double totalAmount;
  final int orderCount;
  final double averageAmount;

  HourlyAnalytics({
    required this.hour,
    required this.totalAmount,
    required this.orderCount,
    required this.averageAmount,
  });
}

class DailyAnalytics {
  final DateTime date;
  final double totalAmount;
  final int orderCount;
  final double averageAmount;

  DailyAnalytics({
    required this.date,
    required this.totalAmount,
    required this.orderCount,
    required this.averageAmount,
  });
}

class PublisherDashboardWidget extends StatefulWidget {
  const PublisherDashboardWidget({super.key});

  @override
  State<PublisherDashboardWidget> createState() =>
      _PublisherDashboardWidgetState();
}

class _PublisherDashboardWidgetState extends State<PublisherDashboardWidget> {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  LineChartBarData? _lineChartBarData;
  List<Map<String, dynamic>> _earningsData = [];
  bool _isLoading = true;
  int _completedOrdersAllTime = 0;
  DateTimeRange? _selectedDateRangeForChart;

  // Analytics enhancement variables
  AnalyticsViewType _currentViewType = AnalyticsViewType.period;
  DateTime? _selectedDay;
  List<HourlyAnalytics> _hourlyAnalytics = [];
  List<DailyAnalytics> _dailyAnalytics = [];
  LineChartBarData? _hourlyChartData;
  LineChartBarData? _dailyChartData;
  bool _isAnalyticsLoading = false;

  @override
  void initState() {
    super.initState();
    _selectedDateRangeForChart = DateTimeRange(
      start: DateTime.now().subtract(const Duration(days: 30)),
      end: DateTime.now(),
    );
    _selectedDay = DateTime.now();
    _loadDashboardData();
    // Load initial analytics data for period view
    _loadDailyAnalytics(_selectedDateRangeForChart!);
  }

  Future<void> _loadDashboardData() async {
    if (!mounted) return;
    setState(() => _isLoading = true);
    await Future.wait([
      _loadEarningsData(),
      _loadCompletedOrders(),
    ]);
    if (mounted) setState(() => _isLoading = false);
  }

  Future<void> _loadEarningsData() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      final historySnapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('history')
          .where('type', isEqualTo: 'Order')
          .where('status', isEqualTo: 'Completed')
          .orderBy('date', descending: true)
          .get();

      _earningsData = historySnapshot.docs.map((doc) {
        final data = doc.data();
        return {
          'date': (data['date'] as Timestamp).toDate(),
          'value': (data['amount'] as num).toDouble(),
        };
      }).toList();

      _updateChartData();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('Error loading earnings: $e',
                  style: const TextStyle(color: Colors.white)),
              backgroundColor:
                  AppTheme.errorColor), // Using your AppTheme.errorColor
        );
      }
      print('Error loading earnings: $e');
    }
  }

  void _updateChartData() {
    if (!mounted) return;
    if (_earningsData.isEmpty || _selectedDateRangeForChart == null) {
      setState(() {
        _lineChartBarData = null;
      });
      return;
    }

    final filteredSpots = _earningsData
        .where((data) =>
            !data['date'].isBefore(_selectedDateRangeForChart!.start) &&
            !data['date'].isAfter(
                _selectedDateRangeForChart!.end.add(const Duration(days: 1))))
        .map((data) {
      double xValue = data['date']
          .difference(_selectedDateRangeForChart!.start)
          .inDays
          .toDouble();
      xValue = xValue < 0 ? 0 : xValue;
      return FlSpot(xValue, data['value'].toDouble());
    }).toList();

    filteredSpots.sort((a, b) => a.x.compareTo(b.x));

    setState(() {
      _lineChartBarData = LineChartBarData(
        spots: filteredSpots,
        isCurved: true,
        color: AppTheme.accentColor, // Using your AppTheme.accentColor
        barWidth: 3,
        isStrokeCapRound: true,
        dotData: FlDotData(
          show: true,
          getDotPainter: (spot, percent, barData, index) => FlDotCirclePainter(
              radius: 4,
              color: AppTheme.accentColor,
              strokeWidth: 1.5,
              strokeColor: AppTheme
                  .componentBackColor), // Assuming componentBackColor for dot stroke
        ),
        belowBarData: BarAreaData(
          show: true,
          gradient: LinearGradient(
            colors: [
              AppTheme.accentColor.withOpacity(0.3),
              AppTheme.accentColor.withOpacity(0.05),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
      );
    });
  }

  Future<void> _loadCompletedOrders() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      final ordersSnapshot = await _firestore
          .collection('orders')
          .where('publisherId', isEqualTo: user.uid)
          .where('status', isEqualTo: 'Completed')
          .get();

      if (mounted) {
        setState(() {
          _completedOrdersAllTime = ordersSnapshot.docs.length;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('Error loading completed orders: $e',
                  style: const TextStyle(color: Colors.white)),
              backgroundColor:
                  AppTheme.errorColor), // Using your AppTheme.errorColor
        );
      }
      print('Error loading completed orders: $e');
    }
  }

  // Analytics data processing methods
  Future<void> _loadHourlyAnalytics(DateTime selectedDay) async {
    if (!mounted) return;
    setState(() => _isAnalyticsLoading = true);

    try {
      final user = _auth.currentUser;
      if (user == null) return;

      final startOfDay =
          DateTime(selectedDay.year, selectedDay.month, selectedDay.day);
      final endOfDay = startOfDay.add(const Duration(days: 1));

      final historySnapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('history')
          .where('type', isEqualTo: 'Order')
          .where('status', isEqualTo: 'Completed')
          .where('date', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .where('date', isLessThan: Timestamp.fromDate(endOfDay))
          .orderBy('date')
          .get();

      // Group earnings by hour
      Map<int, List<double>> hourlyEarnings = {};
      for (int i = 0; i < 24; i++) {
        hourlyEarnings[i] = [];
      }

      for (var doc in historySnapshot.docs) {
        final data = doc.data();
        final date = (data['date'] as Timestamp).toDate();
        final amount = (data['amount'] as num).toDouble();
        final hour = date.hour;

        hourlyEarnings[hour]!.add(amount);
      }

      // Calculate analytics for each hour
      _hourlyAnalytics = hourlyEarnings.entries.map((entry) {
        final hour = entry.key;
        final amounts = entry.value;
        final totalAmount = amounts.fold(0.0, (sum, amount) => sum + amount);
        final orderCount = amounts.length;
        final averageAmount = orderCount > 0 ? totalAmount / orderCount : 0.0;

        return HourlyAnalytics(
          hour: hour,
          totalAmount: totalAmount,
          orderCount: orderCount,
          averageAmount: averageAmount,
        );
      }).toList();

      _updateHourlyChartData();
    } catch (e) {
      if (mounted) {
        print('Error loading hourly analytics: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading hourly analytics: $e',
                style: const TextStyle(color: Colors.white)),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) setState(() => _isAnalyticsLoading = false);
    }
  }

  Future<void> _loadDailyAnalytics(DateTimeRange dateRange) async {
    if (!mounted) return;
    setState(() => _isAnalyticsLoading = true);

    try {
      final user = _auth.currentUser;
      if (user == null) return;

      final historySnapshot = await _firestore
          .collection('users')
          .doc(user.uid)
          .collection('history')
          .where('type', isEqualTo: 'Order')
          .where('status', isEqualTo: 'Completed')
          .where('date',
              isGreaterThanOrEqualTo: Timestamp.fromDate(dateRange.start))
          .where('date',
              isLessThanOrEqualTo: Timestamp.fromDate(
                  dateRange.end.add(const Duration(days: 1))))
          .orderBy('date')
          .get();

      // Group earnings by day
      Map<String, List<double>> dailyEarnings = {};

      for (var doc in historySnapshot.docs) {
        final data = doc.data();
        final date = (data['date'] as Timestamp).toDate();
        final amount = (data['amount'] as num).toDouble();
        final dayKey =
            '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';

        if (!dailyEarnings.containsKey(dayKey)) {
          dailyEarnings[dayKey] = [];
        }
        dailyEarnings[dayKey]!.add(amount);
      }

      // Calculate analytics for each day
      _dailyAnalytics = [];
      DateTime currentDate = dateRange.start;
      while (!currentDate.isAfter(dateRange.end)) {
        final dayKey =
            '${currentDate.year}-${currentDate.month.toString().padLeft(2, '0')}-${currentDate.day.toString().padLeft(2, '0')}';
        final amounts = dailyEarnings[dayKey] ?? [];
        final totalAmount = amounts.fold(0.0, (sum, amount) => sum + amount);
        final orderCount = amounts.length;
        final averageAmount = orderCount > 0 ? totalAmount / orderCount : 0.0;

        _dailyAnalytics.add(DailyAnalytics(
          date: currentDate,
          totalAmount: totalAmount,
          orderCount: orderCount,
          averageAmount: averageAmount,
        ));

        currentDate = currentDate.add(const Duration(days: 1));
      }

      _updateDailyChartData();
    } catch (e) {
      if (mounted) {
        print('Error loading daily analytics: $e');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error loading daily analytics: $e',
                style: const TextStyle(color: Colors.white)),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      if (mounted) setState(() => _isAnalyticsLoading = false);
    }
  }

  void _updateHourlyChartData() {
    if (!mounted || _hourlyAnalytics.isEmpty) {
      setState(() => _hourlyChartData = null);
      return;
    }

    final spots = _hourlyAnalytics
        .where((analytics) => analytics.totalAmount > 0)
        .map((analytics) =>
            FlSpot(analytics.hour.toDouble(), analytics.totalAmount))
        .toList();

    if (spots.isEmpty) {
      setState(() => _hourlyChartData = null);
      return;
    }

    setState(() {
      _hourlyChartData = LineChartBarData(
        spots: spots,
        isCurved: true,
        color: AppTheme.accentColor,
        barWidth: 3,
        isStrokeCapRound: true,
        dotData: FlDotData(
          show: true,
          getDotPainter: (spot, percent, barData, index) => FlDotCirclePainter(
            radius: 4,
            color: AppTheme.accentColor,
            strokeWidth: 1.5,
            strokeColor: AppTheme.componentBackColor,
          ),
        ),
        belowBarData: BarAreaData(
          show: true,
          gradient: LinearGradient(
            colors: [
              AppTheme.accentColor.withOpacity(0.3),
              AppTheme.accentColor.withOpacity(0.05),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
      );
    });
  }

  void _updateDailyChartData() {
    if (!mounted || _dailyAnalytics.isEmpty) {
      setState(() => _dailyChartData = null);
      return;
    }

    final spots = _dailyAnalytics
        .asMap()
        .entries
        .where((entry) => entry.value.totalAmount > 0)
        .map((entry) => FlSpot(entry.key.toDouble(), entry.value.totalAmount))
        .toList();

    if (spots.isEmpty) {
      setState(() => _dailyChartData = null);
      return;
    }

    setState(() {
      _dailyChartData = LineChartBarData(
        spots: spots,
        isCurved: true,
        color: AppTheme.primaryColor,
        barWidth: 3,
        isStrokeCapRound: true,
        dotData: FlDotData(
          show: true,
          getDotPainter: (spot, percent, barData, index) => FlDotCirclePainter(
            radius: 4,
            color: AppTheme.primaryColor,
            strokeWidth: 1.5,
            strokeColor: AppTheme.componentBackColor,
          ),
        ),
        belowBarData: BarAreaData(
          show: true,
          gradient: LinearGradient(
            colors: [
              AppTheme.primaryColor.withOpacity(0.3),
              AppTheme.primaryColor.withOpacity(0.05),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;
    final isMediumScreen = screenWidth >= 600 && screenWidth < 1200;
    final theme = Theme.of(
        context); // This will use the ThemeData provided by your MaterialApp

    if (_auth.currentUser == null) {
      return Scaffold(
        body: Center(
            child: Text('Please sign in to view your dashboard',
                style: theme.textTheme.titleMedium)),
      );
    }

    return StreamBuilder<DocumentSnapshot<Map<String, dynamic>>>(
      stream: _firestore
          .collection('users')
          .doc(_auth.currentUser!.uid)
          .snapshots(),
      builder: (context, userSnapshot) {
        if (userSnapshot.connectionState == ConnectionState.waiting ||
            _isLoading) {
          return Scaffold(
              body: Center(
                  child:
                      CircularProgressIndicator(color: AppTheme.accentColor)));
        }
        if (!userSnapshot.hasData || userSnapshot.data?.data() == null) {
          return Scaffold(
              body: Center(
                  child: Text('User data not found',
                      style: theme.textTheme.titleMedium)));
        }

        final userData = userSnapshot.data!.data()!;
        if (userData['isPublisher'] != true) {
          return Scaffold(
              body: Center(
                  child: Text('This dashboard is for publishers only',
                      style: theme.textTheme.titleMedium)));
        }

        final mainBalance =
            (userData['mainBalance'] as num?)?.toDouble() ?? 0.0;
        final reservedFunds =
            (userData['reservedBalance'] as num?)?.toDouble() ?? 0.0;
        final totalEarnings = mainBalance + reservedFunds;

        return Scaffold(
          backgroundColor:
              AppTheme.backgroundColor, // Using your AppTheme.backgroundColor
          body: SafeArea(
            child: SingleChildScrollView(
              padding: EdgeInsets.all(isSmallScreen ? 16.0 : 24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(bottom: 24.0),
                    child: Text(
                      'Publisher Dashboard',
                      // Assuming your global theme provides Poppins and appropriate text styles
                      style: theme.textTheme.displayMedium?.copyWith(
                          fontWeight: FontWeight.w700,
                          color: AppTheme.textPrimary),
                    ),
                  ),
                  GridView.count(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    crossAxisCount:
                        isSmallScreen ? 1 : (isMediumScreen ? 2 : 4),
                    mainAxisSpacing: isSmallScreen ? 12 : 16,
                    crossAxisSpacing: isSmallScreen ? 12 : 16,
                    childAspectRatio:
                        isSmallScreen ? 2.2 : (isMediumScreen ? 1.8 : 2),
                    children: [
                      _buildMetricCard(
                        context: context,
                        title: 'Available Balance',
                        value: '\$${mainBalance.toStringAsFixed(2)}',
                        icon: FontAwesomeIcons.wallet,
                        iconColor: AppTheme.primaryColor,
                        subtitle: 'Ready to withdraw',
                      ),
                      _buildMetricCard(
                        context: context,
                        title: 'Pending Earnings',
                        value: '\$${reservedFunds.toStringAsFixed(2)}',
                        icon: FontAwesomeIcons.hourglassHalf,
                        iconColor: Colors.orange
                            .shade700, // Consider making this an AppTheme color
                        subtitle: 'Awaiting completion',
                      ),
                      _buildMetricCard(
                        context: context,
                        title: 'Completed Orders',
                        value: _completedOrdersAllTime.toString(),
                        icon: FontAwesomeIcons.solidCheckCircle,
                        iconColor: AppTheme
                            .successColor, // Using your AppTheme.successColor
                        subtitle: 'All time',
                      ),
                      _buildMetricCard(
                        context: context,
                        title: 'Total Revenue',
                        value: '\$${totalEarnings.toStringAsFixed(2)}',
                        icon: FontAwesomeIcons.chartLine,
                        iconColor: AppTheme.accentColor,
                        subtitle: 'All time',
                      ),
                    ],
                  ),
                  const SizedBox(height: 24),
                  LayoutBuilder(
                    builder: (context, constraints) {
                      if (constraints.maxWidth < 800) {
                        return Column(
                          children: [
                            _buildEarningsChartCard(context),
                            const SizedBox(height: 24),
                            OrdersTableWidget(),
                          ],
                        );
                      } else {
                        return Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                                flex: 3,
                                child: _buildEarningsChartCard(context)),
                            const SizedBox(width: 24),
                            Expanded(flex: 2, child: OrdersTableWidget()),
                          ],
                        );
                      }
                    },
                  ),
                  const SizedBox(height: 24),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildEarningsChartCard(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.componentBackColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.cardShadow,
            blurRadius: 20,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and view type selector
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Earnings Analytics',
                style: theme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600, color: AppTheme.textPrimary),
              ),
              _buildViewTypeSelector(context),
            ],
          ),
          const SizedBox(height: 16),

          // Time period selector
          _buildTimePeriodSelector(context),

          const SizedBox(height: 24),

          // Chart area
          SizedBox(
            height: 350,
            child: _buildAnalyticsChart(context),
          ),

          const SizedBox(height: 20),

          // Summary cards
          _buildAnalyticsSummary(context),
        ],
      ),
    );
  }

  Widget _buildViewTypeSelector(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.lightGrey,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildViewTypeTab(
            context,
            'Day View',
            AnalyticsViewType.day,
            FontAwesomeIcons.clock,
          ),
          _buildViewTypeTab(
            context,
            'Period View',
            AnalyticsViewType.period,
            FontAwesomeIcons.chartLine,
          ),
        ],
      ),
    );
  }

  Widget _buildViewTypeTab(BuildContext context, String title,
      AnalyticsViewType type, IconData icon) {
    final theme = Theme.of(context);
    final isSelected = _currentViewType == type;

    return GestureDetector(
      onTap: () {
        setState(() {
          _currentViewType = type;
        });
        _loadAnalyticsData();
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: isSelected ? AppTheme.primaryColor : Colors.transparent,
          borderRadius: BorderRadius.circular(10),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            FaIcon(
              icon,
              size: 14,
              color: isSelected ? Colors.white : AppTheme.textSecondary,
            ),
            const SizedBox(width: 8),
            Text(
              title,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: isSelected ? Colors.white : AppTheme.textSecondary,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _loadAnalyticsData() {
    if (_currentViewType == AnalyticsViewType.day) {
      if (_selectedDay != null) {
        _loadHourlyAnalytics(_selectedDay!);
      }
    } else {
      if (_selectedDateRangeForChart != null) {
        _loadDailyAnalytics(_selectedDateRangeForChart!);
      }
    }
  }

  Widget _buildTimePeriodSelector(BuildContext context) {
    final theme = Theme.of(context);

    if (_currentViewType == AnalyticsViewType.day) {
      return Row(
        children: [
          Icon(FontAwesomeIcons.calendar,
              size: 16, color: AppTheme.textSecondary),
          const SizedBox(width: 8),
          Text(
            'Select Day:',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: TextButton.icon(
              onPressed: _selectDayForAnalytics,
              icon: const Icon(FontAwesomeIcons.calendarDay,
                  size: 16, color: AppTheme.accentColor),
              label: Text(
                _selectedDay != null
                    ? _formatChartDate(_selectedDay!)
                    : 'Select Day',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: AppTheme.accentColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
              style: TextButton.styleFrom(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8)),
              ),
            ),
          ),
        ],
      );
    } else {
      return Row(
        children: [
          Icon(FontAwesomeIcons.chartLine,
              size: 16, color: AppTheme.textSecondary),
          const SizedBox(width: 8),
          Text(
            'Date Range:',
            style: theme.textTheme.bodyMedium?.copyWith(
              color: AppTheme.textSecondary,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: TextButton.icon(
              onPressed: _selectDateRangeForChart,
              icon: const Icon(FontAwesomeIcons.calendarDays,
                  size: 16, color: AppTheme.accentColor),
              label: Text(
                _selectedDateRangeForChart != null
                    ? '${_formatChartDate(_selectedDateRangeForChart!.start)} - ${_formatChartDate(_selectedDateRangeForChart!.end)}'
                    : 'Select Range',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: AppTheme.accentColor,
                  fontWeight: FontWeight.w500,
                ),
              ),
              style: TextButton.styleFrom(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8)),
              ),
            ),
          ),
        ],
      );
    }
  }

  Future<void> _selectDayForAnalytics() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDay ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: AppTheme.primaryColor,
                  onPrimary: Colors.white,
                  surface: AppTheme.componentBackColor,
                  onSurface: AppTheme.textPrimary,
                ),
            dialogBackgroundColor: AppTheme.componentBackColor,
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDay) {
      setState(() {
        _selectedDay = picked;
      });
      _loadHourlyAnalytics(picked);
    }
  }

  Widget _buildAnalyticsChart(BuildContext context) {
    final theme = Theme.of(context);

    if (_currentViewType == AnalyticsViewType.day) {
      return _buildHourlyChart(context);
    } else {
      return _buildDailyChart(context);
    }
  }

  Widget _buildHourlyChart(BuildContext context) {
    final theme = Theme.of(context);

    if (_isAnalyticsLoading) {
      return const Center(
        child: CircularProgressIndicator(color: AppTheme.accentColor),
      );
    }

    if (_hourlyChartData == null || _hourlyChartData!.spots.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            FaIcon(
              FontAwesomeIcons.chartLine,
              size: 48,
              color: AppTheme.textSecondary.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              _selectedDay == null
                  ? 'Select a day to view hourly analytics'
                  : 'No earnings data for selected day',
              style: theme.textTheme.titleMedium?.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    final maxHourlyEarning = _hourlyAnalytics
        .map((analytics) => analytics.totalAmount)
        .reduce((a, b) => a > b ? a : b);

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: true,
          verticalInterval: 4,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: AppTheme.borderColor.withOpacity(0.3),
              strokeWidth: 1,
            );
          },
          getDrawingVerticalLine: (value) {
            return FlLine(
              color: AppTheme.borderColor.withOpacity(0.3),
              strokeWidth: 1,
            );
          },
        ),
        titlesData: FlTitlesData(
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: 2,
              getTitlesWidget: (value, meta) {
                final hour = value.toInt();
                if (hour < 0 || hour > 23) return const SizedBox.shrink();
                return Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    '${hour.toString().padLeft(2, '0')}:00',
                    style: theme.textTheme.bodySmall?.copyWith(
                      fontSize: 10,
                      color: AppTheme.textSecondary,
                    ),
                  ),
                );
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 45,
              getTitlesWidget: (value, meta) {
                if (value == meta.max || value == meta.min) {
                  return const SizedBox.shrink();
                }
                return Padding(
                  padding: const EdgeInsets.only(right: 6),
                  child: Text(
                    '\$${value.toInt()}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      fontSize: 10,
                      color: AppTheme.textSecondary,
                    ),
                  ),
                );
              },
              interval: maxHourlyEarning > 0
                  ? (maxHourlyEarning / 4).clamp(1.0, double.infinity)
                  : 25,
            ),
          ),
          rightTitles:
              const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles:
              const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border(
            bottom: BorderSide(color: AppTheme.borderColor, width: 1),
            left: BorderSide(color: AppTheme.borderColor, width: 1),
          ),
        ),
        minX: 0,
        maxX: 23,
        minY: 0,
        maxY: maxHourlyEarning > 0
            ? (maxHourlyEarning * 1.2).clamp(10.0, double.infinity)
            : 50,
        lineBarsData: [_hourlyChartData!],
        lineTouchData: LineTouchData(
          touchTooltipData: LineTouchTooltipData(
            getTooltipItems: (touchedSpots) {
              return touchedSpots.map((LineBarSpot touchedSpot) {
                final hour = touchedSpot.x.toInt();
                final analytics = _hourlyAnalytics.firstWhere(
                  (a) => a.hour == hour,
                  orElse: () => HourlyAnalytics(
                      hour: hour,
                      totalAmount: 0,
                      orderCount: 0,
                      averageAmount: 0),
                );
                return LineTooltipItem(
                  '${hour.toString().padLeft(2, '0')}:00\n\$${touchedSpot.y.toStringAsFixed(2)}\n${analytics.orderCount} orders',
                  TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                );
              }).toList();
            },
          ),
          handleBuiltInTouches: true,
        ),
      ),
    );
  }

  Widget _buildDailyChart(BuildContext context) {
    final theme = Theme.of(context);

    if (_isAnalyticsLoading) {
      return const Center(
        child: CircularProgressIndicator(color: AppTheme.primaryColor),
      );
    }

    if (_dailyChartData == null || _dailyChartData!.spots.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            FaIcon(
              FontAwesomeIcons.chartLine,
              size: 48,
              color: AppTheme.textSecondary.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              _selectedDateRangeForChart == null
                  ? 'Select a date range to view daily analytics'
                  : 'No earnings data for selected period',
              style: theme.textTheme.titleMedium?.copyWith(
                color: AppTheme.textSecondary,
              ),
            ),
          ],
        ),
      );
    }

    final maxDailyEarning = _dailyAnalytics
        .map((analytics) => analytics.totalAmount)
        .reduce((a, b) => a > b ? a : b);

    final totalDays = _dailyAnalytics.length;

    return LineChart(
      LineChartData(
        gridData: FlGridData(
          show: true,
          drawVerticalLine: true,
          verticalInterval:
              totalDays > 30 ? (totalDays / 5).roundToDouble() : 7,
          getDrawingHorizontalLine: (value) {
            return FlLine(
              color: AppTheme.borderColor.withOpacity(0.3),
              strokeWidth: 1,
            );
          },
          getDrawingVerticalLine: (value) {
            return FlLine(
              color: AppTheme.borderColor.withOpacity(0.3),
              strokeWidth: 1,
            );
          },
        ),
        titlesData: FlTitlesData(
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 30,
              interval: totalDays > 30 ? (totalDays / 5).roundToDouble() : 7,
              getTitlesWidget: (value, meta) {
                final index = value.toInt();
                if (index < 0 || index >= _dailyAnalytics.length) {
                  return const SizedBox.shrink();
                }
                final date = _dailyAnalytics[index].date;
                return Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    '${date.day}/${date.month}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      fontSize: 10,
                      color: AppTheme.textSecondary,
                    ),
                  ),
                );
              },
            ),
          ),
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 45,
              getTitlesWidget: (value, meta) {
                if (value == meta.max || value == meta.min) {
                  return const SizedBox.shrink();
                }
                return Padding(
                  padding: const EdgeInsets.only(right: 6),
                  child: Text(
                    '\$${value.toInt()}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      fontSize: 10,
                      color: AppTheme.textSecondary,
                    ),
                  ),
                );
              },
              interval: maxDailyEarning > 0
                  ? (maxDailyEarning / 4).clamp(1.0, double.infinity)
                  : 25,
            ),
          ),
          rightTitles:
              const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          topTitles:
              const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(
          show: true,
          border: Border(
            bottom: BorderSide(color: AppTheme.borderColor, width: 1),
            left: BorderSide(color: AppTheme.borderColor, width: 1),
          ),
        ),
        minX: 0,
        maxX: totalDays > 1 ? totalDays.toDouble() - 1 : 1,
        minY: 0,
        maxY: maxDailyEarning > 0
            ? (maxDailyEarning * 1.2).clamp(10.0, double.infinity)
            : 50,
        lineBarsData: [_dailyChartData!],
        lineTouchData: LineTouchData(
          touchTooltipData: LineTouchTooltipData(
            getTooltipItems: (touchedSpots) {
              return touchedSpots
                  .map((LineBarSpot touchedSpot) {
                    final index = touchedSpot.x.toInt();
                    if (index >= 0 && index < _dailyAnalytics.length) {
                      final analytics = _dailyAnalytics[index];
                      return LineTooltipItem(
                        '${analytics.date.day}/${analytics.date.month}\n\$${touchedSpot.y.toStringAsFixed(2)}\n${analytics.orderCount} orders',
                        TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      );
                    }
                    return null;
                  })
                  .where((item) => item != null)
                  .cast<LineTooltipItem>()
                  .toList();
            },
          ),
          handleBuiltInTouches: true,
        ),
      ),
    );
  }

  Widget _buildAnalyticsSummary(BuildContext context) {
    if (_currentViewType == AnalyticsViewType.day) {
      return _buildHourlySummary(context);
    } else {
      return _buildDailySummary(context);
    }
  }

  Widget _buildHourlySummary(BuildContext context) {
    if (_hourlyAnalytics.isEmpty) {
      return const SizedBox.shrink();
    }

    final totalEarnings = _hourlyAnalytics.fold(
        0.0, (sum, analytics) => sum + analytics.totalAmount);
    final totalOrders = _hourlyAnalytics.fold(
        0, (sum, analytics) => sum + analytics.orderCount);
    final peakHour = _hourlyAnalytics
        .reduce((a, b) => a.totalAmount > b.totalAmount ? a : b);
    final averagePerHour = totalOrders > 0 ? totalEarnings / 24 : 0.0;

    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            context,
            'Total Earnings',
            '\$${totalEarnings.toStringAsFixed(2)}',
            AppTheme.successColor,
            FontAwesomeIcons.dollarSign,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            context,
            'Peak Hour',
            '${peakHour.hour.toString().padLeft(2, '0')}:00',
            AppTheme.accentColor,
            FontAwesomeIcons.clock,
            subtitle: '\$${peakHour.totalAmount.toStringAsFixed(2)}',
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            context,
            'Avg/Hour',
            '\$${averagePerHour.toStringAsFixed(2)}',
            AppTheme.primaryColor,
            FontAwesomeIcons.chartLine,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            context,
            'Total Orders',
            totalOrders.toString(),
            AppTheme.infoColor,
            FontAwesomeIcons.listCheck,
          ),
        ),
      ],
    );
  }

  Widget _buildDailySummary(BuildContext context) {
    if (_dailyAnalytics.isEmpty) {
      return const SizedBox.shrink();
    }

    final totalEarnings = _dailyAnalytics.fold(
        0.0, (sum, analytics) => sum + analytics.totalAmount);
    final totalOrders =
        _dailyAnalytics.fold(0, (sum, analytics) => sum + analytics.orderCount);
    final peakDay =
        _dailyAnalytics.reduce((a, b) => a.totalAmount > b.totalAmount ? a : b);
    final averagePerDay = _dailyAnalytics.isNotEmpty
        ? totalEarnings / _dailyAnalytics.length
        : 0.0;

    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            context,
            'Total Earnings',
            '\$${totalEarnings.toStringAsFixed(2)}',
            AppTheme.successColor,
            FontAwesomeIcons.dollarSign,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            context,
            'Peak Day',
            '${peakDay.date.day}/${peakDay.date.month}',
            AppTheme.accentColor,
            FontAwesomeIcons.calendar,
            subtitle: '\$${peakDay.totalAmount.toStringAsFixed(2)}',
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            context,
            'Avg/Day',
            '\$${averagePerDay.toStringAsFixed(2)}',
            AppTheme.primaryColor,
            FontAwesomeIcons.chartLine,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildSummaryCard(
            context,
            'Total Orders',
            totalOrders.toString(),
            AppTheme.infoColor,
            FontAwesomeIcons.listCheck,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(
    BuildContext context,
    String title,
    String value,
    Color color,
    IconData icon, {
    String? subtitle,
  }) {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3), width: 1),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  title,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: color,
                    fontWeight: FontWeight.w600,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              FaIcon(icon, size: 16, color: color),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: theme.textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: theme.textTheme.bodySmall?.copyWith(
                color: color.withOpacity(0.8),
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ],
      ),
    );
  }

  String _formatChartDate(DateTime date) {
    return '${_getMonthAbbreviation(date.month)} ${date.day}, ${date.year}';
  }

  String _getMonthAbbreviation(int month) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return months[month - 1];
  }

  Future<void> _selectDateRangeForChart() async {
    DateTimeRange? picked = await showDateRangePicker(
      context: context,
      initialDateRange: _selectedDateRangeForChart,
      firstDate: DateTime(2020),
      lastDate: DateTime.now().add(const Duration(days: 1)),
      builder: (context, child) {
        // Assuming your global theme for DatePicker is set or you customize it here
        return Theme(
          data: Theme.of(context).copyWith(
            // Further customize if needed, or rely on global theme
            colorScheme:
                (Theme.of(context).colorScheme ?? const ColorScheme.light())
                    .copyWith(
              primary: AppTheme.primaryColor,
              onPrimary: AppTheme.componentBackColor, // Text on primary color
              surface: AppTheme.componentBackColor,
              onSurface: AppTheme.textPrimary,
            ),
            dialogBackgroundColor: AppTheme.componentBackColor,
          ),
          child: Center(
            child: ConstrainedBox(
              constraints: const BoxConstraints(maxWidth: 400, maxHeight: 600),
              child: child,
            ),
          ),
        );
      },
    );
    if (picked != null && picked != _selectedDateRangeForChart) {
      if (mounted) {
        setState(() {
          _selectedDateRangeForChart = picked;
        });
        _updateChartData();
      }
    }
  }

  Widget _buildMetricCard({
    required BuildContext context,
    required String title,
    required String value,
    required IconData icon,
    required Color iconColor,
    String? trend,
    bool? isPositive,
    required String subtitle,
  }) {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme
            .componentBackColor, // Using your AppTheme.componentBackColor
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.cardShadow ??
                Colors.grey.withOpacity(0.08), // Using your AppTheme.cardShadow
            blurRadius: 15,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(title,
                  style: theme.textTheme.titleMedium?.copyWith(
                      color: AppTheme.textSecondary,
                      fontWeight: FontWeight.w500)),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: iconColor.withOpacity(0.15),
                  shape: BoxShape.circle,
                ),
                child: FaIcon(icon, size: 18, color: iconColor),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(value,
              style: theme.textTheme.displaySmall?.copyWith(
                  fontWeight: FontWeight.w700, color: AppTheme.textPrimary)),
          const SizedBox(height: 4),
          Row(
            children: [
              if (trend != null && isPositive != null)
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      isPositive
                          ? Icons.arrow_upward_rounded
                          : Icons.arrow_downward_rounded,
                      color: isPositive
                          ? AppTheme.successColor
                          : AppTheme.errorColor,
                      size: 16,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      trend,
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: isPositive
                            ? AppTheme.successColor
                            : AppTheme.errorColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(width: 6),
                  ],
                ),
              Expanded(
                child: Text(
                  subtitle,
                  style: theme.textTheme.bodySmall
                      ?.copyWith(color: AppTheme.textSecondary),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class OrdersTableWidget extends StatefulWidget {
  const OrdersTableWidget({super.key});

  @override
  State<OrdersTableWidget> createState() => _OrdersTableWidgetState();
}

class _OrdersTableWidgetState extends State<OrdersTableWidget> {
  String _selectedTab = 'All';
  final TextEditingController _searchController = TextEditingController();
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _auth = FirebaseAuth.instance;
  List<Map<String, dynamic>> _orders = [];
  bool _isLoading = true;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _loadOrders();
    _searchController.addListener(() {
      if (mounted) setState(() {});
    });
  }

  Future<void> _loadOrders() async {
    if (!mounted) return;
    setState(() => _isLoading = true);
    try {
      final user = _auth.currentUser;
      if (user == null) {
        if (mounted) setState(() => _isLoading = false);
        return;
      }

      final ordersSnapshot = await _firestore
          .collection('orders')
          .where('publisherId', isEqualTo: user.uid)
          .orderBy('orderDate', descending: true)
          .limit(50)
          .get();

      _orders = ordersSnapshot.docs.map((doc) {
        final data = doc.data();
        return {
          'orderId': doc.id,
          'website': data['websiteUrl'] ?? 'N/A',
          'amount': (data['totalPrice'] as num?)?.toDouble() ?? 0.0,
          'status':
              (data['status'] ?? 'Unknown').toString().capitalizeFirstLetter(),
          'createdAt': data['orderDate'] as Timestamp?,
        };
      }).toList();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
              content: Text('Error loading orders: $e',
                  style: const TextStyle(
                      color:
                          Colors.white)), // Assuming white text on error color
              backgroundColor: AppTheme.errorColor),
        );
      }
      print('Error loading orders: $e');
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  List<Map<String, dynamic>> get _filteredAndSearchedOrders {
    List<Map<String, dynamic>> tempOrders = [];
    if (_selectedTab == 'All') {
      tempOrders = _orders;
    } else {
      tempOrders =
          _orders.where((order) => order['status'] == _selectedTab).toList();
    }

    if (_searchController.text.isNotEmpty) {
      final searchTerm = _searchController.text.toLowerCase();
      tempOrders = tempOrders
          .where((order) =>
              order['orderId'].toString().toLowerCase().contains(searchTerm) ||
              order['website'].toString().toLowerCase().contains(searchTerm))
          .toList();
    }
    return tempOrders;
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  Widget _buildTab(BuildContext context, String title) {
    final theme = Theme.of(context);
    final bool isSelected = _selectedTab == title;
    return GestureDetector(
      onTap: () {
        if (mounted) setState(() => _selectedTab = title);
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color:
              isSelected ? AppTheme.primaryColor : AppTheme.componentBackColor,
          borderRadius: BorderRadius.circular(10),
          border: isSelected
              ? null
              : Border.all(
                  color: AppTheme.borderColor ?? Colors.grey.shade300,
                  width: 1),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                      color: AppTheme.primaryColor.withOpacity(0.3),
                      blurRadius: 6,
                      offset: const Offset(0, 3))
                ]
              : [],
        ),
        child: Text(
          title,
          style: theme.textTheme.bodyMedium?.copyWith(
            color: isSelected
                ? (AppTheme.componentBackColor ?? Colors.white)
                : AppTheme.textPrimary, // Ensure contrast for selected tab text
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
          ),
        ),
      ),
    );
  }

  String _formatDate(Timestamp? timestamp) {
    if (timestamp == null) return 'N/A';
    final date = timestamp.toDate();
    return '${date.day} ${_getMonthAbbreviation(date.month)}, ${date.year}';
  }

  String _getMonthAbbreviation(int month) {
    const months = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec'
    ];
    return months[month - 1];
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final displayedOrders = _filteredAndSearchedOrders;

    return Container(
      constraints: const BoxConstraints(minHeight: 400, maxHeight: 600),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme
            .componentBackColor, // Using your AppTheme.componentBackColor
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
              color: AppTheme.cardShadow ??
                  Colors.grey
                      .withOpacity(0.08), // Using your AppTheme.cardShadow
              blurRadius: 20,
              offset: const Offset(0, 5))
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text('Recent Orders',
                  style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimary)),
              SizedBox(
                width: MediaQuery.of(context).size.width > 600 ? 250 : 180,
                child: TextField(
                  controller: _searchController,
                  style: TextStyle(
                      color: AppTheme
                          .textPrimary), // Ensure input text color matches theme
                  decoration: InputDecoration(
                    hintText: 'Search by ID or Website',
                    hintStyle: TextStyle(
                        color: AppTheme.textSecondary.withOpacity(0.8)),
                    prefixIcon: Icon(FontAwesomeIcons.magnifyingGlass,
                        size: 16, color: AppTheme.textSecondary),
                    // Assuming your global InputDecorationTheme is set via AppTheme.
                    // If not, you might need to style border, fillColor etc. here or in your AppTheme's ThemeData
                    fillColor:
                        AppTheme.lightGrey, // from user's AppColors -> AppTheme
                    filled: true,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide:
                          BorderSide(color: AppTheme.accentColor, width: 1.5),
                    ),
                    contentPadding: const EdgeInsets.symmetric(
                        horizontal: 20, vertical: 16),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              children: [
                _buildTab(context, 'All'),
                const SizedBox(width: 10),
                _buildTab(context, 'Pending'),
                const SizedBox(width: 10),
                _buildTab(context, 'Approved'),
                const SizedBox(width: 10),
                _buildTab(context, 'In Progress'),
                const SizedBox(width: 10),
                _buildTab(context, 'Completed'),
                const SizedBox(width: 10),
                _buildTab(context, 'Cancelled'),
              ],
            ),
          ),
          const SizedBox(height: 16),
          if (_isLoading)
            const Expanded(
                child: Center(
                    child:
                        CircularProgressIndicator(color: AppTheme.accentColor)))
          else if (displayedOrders.isEmpty)
            Expanded(
              child: Center(
                child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      FaIcon(FontAwesomeIcons.fileCircleXmark,
                          size: 48,
                          color: AppTheme.textSecondary.withOpacity(0.5)),
                      const SizedBox(height: 16),
                      Text('No orders found.',
                          style: theme.textTheme.titleMedium
                              ?.copyWith(color: AppTheme.textSecondary)),
                      if (_searchController.text.isNotEmpty)
                        Text('Try adjusting your search or filter.',
                            style: theme.textTheme.bodyMedium
                                ?.copyWith(color: AppTheme.textSecondary)),
                    ]),
              ),
            )
          else
            Expanded(
              child: ListView.separated(
                controller: _scrollController,
                itemCount: displayedOrders.length,
                shrinkWrap: true,
                physics: const BouncingScrollPhysics(),
                separatorBuilder: (context, index) => Divider(
                    height: 1,
                    thickness: 1,
                    color: (AppTheme.borderColor ?? Colors.grey.shade200)
                        .withOpacity(0.5),
                    indent: 16,
                    endIndent: 16),
                itemBuilder: (context, index) {
                  final order = displayedOrders[index];
                  return OrderPremiumCard(
                    orderId: order['orderId'],
                    website: order['website'],
                    amount: order['amount'],
                    status: order['status'],
                    date: _formatDate(order['createdAt']),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }
}

class OrderPremiumCard extends StatelessWidget {
  final String orderId;
  final String website;
  final double amount;
  final String status;
  final String date;

  const OrderPremiumCard({
    super.key,
    required this.orderId,
    required this.website,
    required this.amount,
    required this.status,
    required this.date,
  });

  Color _statusColor(String status, BuildContext context) {
    switch (status.toLowerCase()) {
      case 'completed':
        return AppTheme.successColor;
      case 'pending':
        return AppTheme
            .warningColor; // Using your AppTheme.warningColor for pending
      case 'approved':
        return AppTheme
            .infoColor; // Using your AppTheme.infoColor for approved (or accentColor)
      case 'in progress':
        return Colors.purple[600]!; // Match the color used in the orders page
      case 'cancelled':
      case 'declined':
        return AppTheme.errorColor;
      default:
        return AppTheme.textSecondary;
    }
  }

  IconData _statusIcon(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return FontAwesomeIcons.solidCircleCheck;
      case 'pending':
        return FontAwesomeIcons.solidClock;
      case 'approved':
        return FontAwesomeIcons.solidThumbsUp;
      case 'in progress':
        return FontAwesomeIcons.spinner;
      case 'cancelled':
      case 'declined':
        return FontAwesomeIcons.solidCircleXmark;
      default:
        return FontAwesomeIcons.solidQuestionCircle;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final statusColor = _statusColor(status, context);

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          // TODO: Implement order detail navigation or action
          print("Tapped on order: $orderId");
        },
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: statusColor.withOpacity(0.12),
                  shape: BoxShape.circle,
                ),
                child:
                    FaIcon(_statusIcon(status), color: statusColor, size: 20),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      website,
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimary,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      "ID: $orderId",
                      style: theme.textTheme.bodySmall?.copyWith(
                        color: AppTheme.textSecondary,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '\$${amount.toStringAsFixed(2)}',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    date,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: AppTheme.textSecondary,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

extension StringCasingExtension on String {
  String capitalizeFirstLetter() =>
      isNotEmpty ? '${this[0].toUpperCase()}${substring(1).toLowerCase()}' : '';
}
